#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能完整验证脚本
"""

import sys
import os
import time
import requests
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_api_server():
    """测试API服务器"""
    print_section("API服务器测试")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8080/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务器运行正常")
            data = response.json()
            print(f"   服务器状态: {data.get('status', 'unknown')}")
            print(f"   服务器时间: {data.get('timestamp', 'unknown')}")
            return True
        else:
            print(f"❌ API服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API服务器连接失败: {e}")
        return False

def test_user_authentication():
    """测试用户认证"""
    print_section("用户认证测试")
    
    try:
        # 测试登录
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            "http://localhost:8080/api/auth/login",
            json=login_data,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                token = data.get('data', {}).get('token')
                print("✅ 用户登录成功")
                print(f"   用户名: {data.get('data', {}).get('username', 'unknown')}")
                print(f"   角色: {data.get('data', {}).get('role', 'unknown')}")
                return token
            else:
                print(f"❌ 登录失败: {data.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求异常: {e}")
        return None

def test_file_management(token):
    """测试文件管理功能"""
    print_section("文件管理功能测试")
    
    if not token:
        print("❌ 无有效Token，跳过文件管理测试")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取共享文件夹
        response = requests.get(
            "http://localhost:8080/api/files/folders",
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                folders = data.get('data', {}).get('folders', [])
                print(f"✅ 获取共享文件夹成功，共 {len(folders)} 个")
                
                for folder in folders[:3]:  # 显示前3个
                    print(f"   📁 {folder.get('name', 'Unknown')}")
                    print(f"      路径: {folder.get('path', 'Unknown')}")
                    print(f"      文件数: {folder.get('file_count', 0)}")
                
                # 如果有文件夹，测试获取文件列表
                if folders:
                    folder_id = folders[0].get('id')
                    if folder_id:
                        return test_file_list(token, folder_id)
                
                return True
            else:
                print(f"❌ 获取共享文件夹失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 请求共享文件夹失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 文件管理请求异常: {e}")
        return False

def test_file_list(token, folder_id):
    """测试文件列表获取"""
    print_section("文件列表测试")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"http://localhost:8080/api/files/folders/{folder_id}/files",
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                files = data.get('data', {}).get('files', [])
                print(f"✅ 获取文件列表成功，共 {len(files)} 个文件")
                
                for file_info in files[:3]:  # 显示前3个
                    print(f"   📄 {file_info.get('filename', 'Unknown')}")
                    print(f"      大小: {file_info.get('file_size', 0)} 字节")
                    print(f"      类型: {file_info.get('extension', 'Unknown')}")
                
                return True
            else:
                print(f"❌ 获取文件列表失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 请求文件列表失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 文件列表请求异常: {e}")
        return False

def test_search_functionality(token):
    """测试搜索功能"""
    print_section("搜索功能测试")
    
    if not token:
        print("❌ 无有效Token，跳过搜索测试")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试文本搜索
        search_data = {
            "query": "test",
            "type": "text"
        }
        
        response = requests.post(
            "http://localhost:8080/api/search",
            json=search_data,
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                results = data.get('data', {}).get('results', [])
                print(f"✅ 文本搜索成功，找到 {len(results)} 个结果")
                return True
            else:
                print(f"❌ 搜索失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 搜索请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 搜索请求异常: {e}")
        return False

def test_system_stats(token):
    """测试系统统计"""
    print_section("系统统计测试")
    
    if not token:
        print("❌ 无有效Token，跳过统计测试")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            "http://localhost:8080/api/admin/stats",
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                stats = data.get('data', {})
                print("✅ 系统统计获取成功")
                print(f"   用户总数: {stats.get('total_users', 0)}")
                print(f"   文件总数: {stats.get('total_files', 0)}")
                print(f"   在线用户: {stats.get('online_users', 0)}")
                return True
            else:
                print(f"❌ 获取统计失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 统计请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 统计请求异常: {e}")
        return False

def check_gui_process():
    """检查GUI进程是否运行"""
    print_section("GUI进程检查")
    
    try:
        import psutil
        
        # 查找Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('main.py' in arg for arg in cmdline):
                        python_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if python_processes:
            print(f"✅ 找到 {len(python_processes)} 个主程序进程")
            for proc in python_processes:
                print(f"   PID: {proc['pid']}")
            return True
        else:
            print("❌ 未找到主程序进程")
            return False
            
    except ImportError:
        print("⚠️ psutil未安装，无法检查进程")
        return None
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def main():
    """主函数"""
    print_header("企业级文件共享系统 - 完整功能验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # 1. 检查GUI进程
    results['gui_process'] = check_gui_process()
    
    # 2. 测试API服务器
    results['api_server'] = test_api_server()
    
    # 3. 测试用户认证
    token = test_user_authentication()
    results['authentication'] = token is not None
    
    # 4. 测试文件管理
    results['file_management'] = test_file_management(token)
    
    # 5. 测试搜索功能
    results['search'] = test_search_functionality(token)
    
    # 6. 测试系统统计
    results['system_stats'] = test_system_stats(token)
    
    # 总结结果
    print_header("验证结果总结")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result is True)
    
    print(f"📊 总测试项目: {total_tests}")
    print(f"✅ 通过测试: {passed_tests}")
    print(f"❌ 失败测试: {total_tests - passed_tests}")
    print(f"📈 通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result is True else "❌ 失败" if result is False else "⚠️ 跳过"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有功能验证通过！系统运行正常！")
    elif passed_tests >= total_tests * 0.8:
        print("\n⚠️ 大部分功能正常，少数功能需要检查")
    else:
        print("\n❌ 多个功能存在问题，需要进一步排查")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
