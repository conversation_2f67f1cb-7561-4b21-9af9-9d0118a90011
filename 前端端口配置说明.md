# 前端端口配置功能说明

## 功能概述

现在您的文件分享系统支持独立配置前端端口，这对于商用部署非常重要，可以避免端口冲突并提供更灵活的部署选项。

## 新增功能

### 1. 桌面应用设置界面

在桌面应用的"系统设置" → "服务器设置"中，现在包含：

- **API端口**: 后端API服务器端口（默认: 8081）
- **前端端口**: 前端Web服务器端口（默认: 8082）
- **端口检查**: 每个端口都有独立的"检查端口"按钮
- **端口状态显示**: 主界面显示两个端口的运行状态

### 2. 配置文件支持

在 `backend/config/config.yaml` 中：

```yaml
server:
  port: 8081          # API服务器端口
  frontend_port: 8082 # 前端服务器端口
  host: 0.0.0.0
  debug: false
  max_workers: 10
  timeout: 30
```

### 3. 登录页面功能

- **服务器地址配置**: 用户可以在登录页面配置服务器地址
- **自动连接检测**: 实时检测服务器连接状态
- **设置持久化**: 登录信息和服务器配置本地保存

## 使用方法

### 方法一：通过桌面应用设置

1. 启动桌面应用程序
2. 点击"系统设置"按钮
3. 在"服务器设置"标签页中：
   - 修改"API端口"（如需要）
   - 修改"前端端口"
   - 点击"检查端口"验证端口可用性
4. 点击"保存设置"
5. 选择是否立即重启服务器应用新端口

### 方法二：直接修改配置文件

1. 编辑 `backend/config/config.yaml`
2. 修改 `server.frontend_port` 值
3. 重启系统

## 端口配置建议

### 开发环境
- API端口: 8081
- 前端端口: 8082

### 生产环境
- API端口: 80 或 443（配合反向代理）
- 前端端口: 8080 或自定义端口

### 多实例部署
- 实例1: API=8081, 前端=8082
- 实例2: API=8091, 前端=8092
- 实例3: API=8101, 前端=8102

## 安全注意事项

1. **防火墙配置**: 确保配置的端口在防火墙中开放
2. **端口冲突**: 使用"检查端口"功能避免端口被占用
3. **权限要求**: 使用1024以下端口需要管理员权限
4. **网络访问**: 确保客户端可以访问配置的端口

## 故障排除

### 端口被占用
- 使用"检查端口"功能确认端口状态
- 更换其他可用端口
- 检查是否有其他程序占用端口

### 无法访问前端
- 确认前端端口配置正确
- 检查防火墙设置
- 验证服务器是否正常启动

### 登录页面无法连接服务器
- 检查API端口是否正确
- 确认API服务器正在运行
- 验证网络连接

## 技术实现

### 配置管理
- 使用YAML配置文件存储端口设置
- 支持运行时动态修改配置
- 配置变更后可选择立即生效或重启生效

### 端口验证
- 实时检测端口可用性
- 防止端口冲突
- 提供详细的端口状态信息

### 服务器管理
- 独立的API服务器和前端服务器
- 支持单独重启各个服务
- 自动端口绑定和释放

## 更新日志

### v1.0.1 (当前版本)
- ✅ 添加前端端口独立配置
- ✅ 桌面应用设置界面更新
- ✅ 端口检查功能
- ✅ 登录页面服务器配置
- ✅ 配置文件支持
- ✅ 端口状态显示优化

这个更新使您的文件分享系统更适合商业部署，提供了更大的灵活性和更好的用户体验！
