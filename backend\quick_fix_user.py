#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复用户密码问题
"""

import pymysql
import hashlib

def quick_fix():
    """快速修复管理员密码"""
    try:
        print("正在连接数据库...")
        
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        print("数据库连接成功")
        
        with connection.cursor() as cursor:
            # 使用简单的SHA256哈希
            password = "admin123"
            salt = "simple_salt"
            password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            
            print(f"生成密码哈希: {password_hash}")
            
            # 删除现有的admin用户（如果存在）
            cursor.execute("DELETE FROM users WHERE username = 'admin'")
            
            # 插入新的admin用户
            cursor.execute("""
                INSERT INTO users (username, password_hash, salt, full_name, is_admin, user_group, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, ('admin', password_hash, salt, '系统管理员', True, 'admin', True))
            
            connection.commit()
            print("✓ 管理员用户创建成功")
            print("用户名: admin")
            print("密码: admin123")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"修复失败: {e}")
        return False

if __name__ == "__main__":
    print("快速修复管理员密码")
    print("=" * 30)
    quick_fix()
