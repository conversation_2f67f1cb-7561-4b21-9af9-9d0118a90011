# 企业级文件共享系统

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/example/file-share-system)
[![Python](https://img.shields.io/badge/python-3.7+-green.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-orange.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-production%20ready-brightgreen.svg)](README.md)

> 🚀 一套完整的企业级文件共享解决方案，支持用户管理、权限控制、双搜索引擎、缩略图生成、加密下载等功能。

## 📋 目录

- [功能特性](#-功能特性)
- [技术架构](#-技术架构)
- [快速开始](#-快速开始)
- [系统要求](#-系统要求)
- [安装部署](#-安装部署)
- [使用指南](#-使用指南)
- [API文档](#-api文档)
- [项目结构](#-项目结构)
- [开发指南](#-开发指南)
- [常见问题](#-常见问题)
- [更新日志](#-更新日志)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## 🎯 功能特性

### 核心功能
- ✅ **用户管理系统** - 完整的用户增删改查、权限控制
- ✅ **文件管理系统** - 共享文件夹管理、文件操作
- ✅ **双搜索引擎** - 文本搜索 + 图像识别搜索
- ✅ **缩略图服务** - 多格式图像缩略图生成
- ✅ **加密下载** - N次下载后自动加密保护
- ✅ **实时监控** - 系统性能和用户活动监控
- ✅ **活动日志** - 详细的用户行为记录
- ✅ **通知系统** - 滚动通知和截图支持

### 企业级特性
- 🏢 **多用户管理** - 支持用户组和权限控制
- 🔒 **安全控制** - 内网/外网访问控制
- 📊 **监控统计** - 实时系统监控和使用统计
- 📝 **审计日志** - 详细的用户行为记录
- 🌐 **API接口** - 完整的RESTful API
- 🖥️ **GUI管理** - 基于tkinter的桌面管理界面

### 安全特性
- 🔐 **用户认证** - 安全密码哈希、JWT Token认证
- 🚫 **访问控制** - 基于角色的权限控制
- 🔒 **数据保护** - 加密下载、敏感文件保护
- 📝 **审计日志** - 用户行为记录、安全事件监控

## 🏗️ 技术架构

```
后端语言: Python 3.7+
Web框架: Flask + Flask-SocketIO
数据库: MySQL 8.0
ORM: SQLAlchemy
GUI框架: tkinter
搜索引擎: Whoosh + 简化版本
图像处理: Pillow + OpenCV
系统监控: psutil
加密: cryptography
```

## ⚡ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/example/file-share-system.git
cd file-share-system
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置数据库
```bash
# 修改数据库配置
# backend/config/settings.py

# 初始化数据库
cd backend
python init_database.py
```

### 4. 启动系统
```bash
# 启动完整版（推荐）
python main.py

# 或启动简化版
python main_simple.py
```

### 5. 访问系统
- **GUI管理界面**: 自动弹出桌面窗口
- **API接口**: http://localhost:8080
- **健康检查**: http://localhost:8080/api/health

### 6. 默认登录
- **用户名**: `admin`
- **密码**: `admin123`

## 💻 系统要求

### 最低要求
- **操作系统**: Windows 7/10/11
- **Python**: 3.7+
- **内存**: 4GB+
- **硬盘**: 10GB+
- **数据库**: MySQL 5.7+ (完整版需要)

### 推荐配置
- **操作系统**: Windows 10/11
- **Python**: 3.9+
- **内存**: 8GB+
- **硬盘**: 50GB+
- **数据库**: MySQL 8.0+

## 📦 安装部署

### 方式一：完整安装
```bash
# 1. 安装Python依赖
pip install flask flask-socketio sqlalchemy pymysql
pip install pillow opencv-python psutil cryptography
pip install whoosh requests

# 2. 安装MySQL数据库
# 下载并安装MySQL 8.0

# 3. 创建数据库
mysql -u root -p
CREATE DATABASE file_share_system CHARACTER SET utf8mb4;

# 4. 初始化系统
cd backend
python init_database.py
python main.py
```

### 方式二：简化安装（无数据库）
```bash
# 1. 安装基础依赖
pip install tkinter psutil

# 2. 启动简化版
cd backend
python main_simple.py
```

## 📖 使用指南

### GUI管理界面
1. **主控制面板** - 系统状态监控、服务器控制
2. **用户管理** - 用户增删改查、权限设置
3. **文件管理** - 共享文件夹管理、文件操作
4. **系统监控** - 实时性能监控、活动日志
5. **通知系统** - 系统通知发送、滚动显示

### API接口使用
```python
import requests

# 登录获取Token
response = requests.post('http://localhost:8080/api/auth/login', json={
    'username': 'admin',
    'password': 'admin123'
})
token = response.json()['data']['token']

# 使用Token访问API
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8080/api/admin/users', headers=headers)
```

详细使用说明请参考：[使用指南.md](backend/使用指南.md)

## 📚 API文档

完整的API文档请参考：[API文档.md](API文档.md)

### 主要接口
- **认证系统**: `/api/auth/*`
- **用户管理**: `/api/admin/users/*`
- **文件管理**: `/api/files/*`
- **搜索功能**: `/api/search`
- **系统管理**: `/api/admin/*`

## 📁 项目结构

```
file-share-system/
├── backend/                         # 后端代码
│   ├── main.py                      # 完整版主程序
│   ├── main_simple.py               # 简化版主程序
│   ├── config/                      # 配置模块
│   ├── models/                      # 数据模型
│   ├── services/                    # 业务服务
│   ├── api/                         # API接口
│   ├── gui/                         # GUI界面
│   └── utils/                       # 工具模块
├── docs/                            # 文档目录
├── tests/                           # 测试目录
├── API文档.md                       # API接口文档
├── 使用指南.md                      # 使用说明
├── 项目总结.md                      # 项目总结
└── README.md                        # 项目说明
```

## 🛠️ 开发指南

### 开发环境搭建
```bash
# 1. 克隆项目
git clone https://github.com/example/file-share-system.git

# 2. 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 3. 安装开发依赖
pip install -r requirements-dev.txt

# 4. 运行测试
cd backend
python test_api_endpoints.py
```

### 代码规范
- 使用Python PEP 8编码规范
- 函数和类需要添加文档字符串
- 重要功能需要编写单元测试
- 提交代码前运行测试套件

### 扩展开发
系统采用模块化设计，支持功能扩展：
- **服务扩展**: 在`services/`目录添加新服务
- **API扩展**: 在`api/`目录添加新接口
- **GUI扩展**: 在`gui/`目录添加新窗口
- **模型扩展**: 在`models/`目录添加新模型

## ❓ 常见问题

### Q: 系统启动失败？
A: 检查Python版本、依赖安装、数据库连接等。

### Q: 无法登录系统？
A: 运行密码修复脚本：`python fix_admin_password.py`

### Q: API接口无响应？
A: 检查服务器是否启动，端口是否被占用。

### Q: 文件上传失败？
A: 检查文件大小限制、文件类型限制、磁盘空间等。

更多问题请参考：[使用指南.md](backend/使用指南.md)

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 初始版本发布
- ✅ 完整的用户管理功能
- ✅ 文件管理和下载功能
- ✅ 双搜索引擎支持
- ✅ 缩略图服务
- ✅ GUI管理界面
- ✅ 完整的API接口
- ✅ 详细的文档资料

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📚 文档改进
- 🎨 UI/UX改进
- ⚡ 性能优化
- 🧪 测试用例

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **项目主页**: https://github.com/example/file-share-system
- **问题反馈**: https://github.com/example/file-share-system/issues
- **邮箱**: <EMAIL>
- **QQ群**: 123456789

---

<div align="center">

**🎉 感谢使用企业级文件共享系统！**

如果这个项目对您有帮助，请给我们一个 ⭐ Star！

</div>
