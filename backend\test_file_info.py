#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件信息读取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import DatabaseManager
from services.file_service import FileService

def test_file_info():
    """测试文件信息读取"""
    print("🔍 测试文件信息读取功能")
    print("=" * 50)
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        db_manager.initialize()

        # 初始化文件服务
        file_service = FileService(db_manager)
        
        # 获取共享文件夹列表
        print("📁 获取共享文件夹列表...")
        folders_result = file_service.get_shared_folders()
        
        if not folders_result.get('success', False):
            print(f"❌ 获取共享文件夹失败: {folders_result.get('error', '未知错误')}")
            return
        
        folders = folders_result.get('folders', [])
        print(f"✅ 找到 {len(folders)} 个共享文件夹")
        
        for folder in folders:
            print(f"\n📂 文件夹: {folder.get('name', 'Unknown')}")
            print(f"   路径: {folder.get('path', 'Unknown')}")
            print(f"   文件数: {folder.get('file_count', 0)}")
            
            folder_id = folder.get('id')
            if folder_id:
                # 获取文件列表
                print(f"\n📄 获取文件夹 {folder_id} 的文件列表...")
                files_result = file_service.get_folder_files(folder_id, 1, 100, None)
                
                if files_result.get('success', False):
                    files = files_result.get('files', [])
                    print(f"✅ 找到 {len(files)} 个文件")
                    
                    for i, file_info in enumerate(files[:5]):  # 只显示前5个文件
                        print(f"\n   文件 {i+1}:")
                        print(f"     ID: {file_info.get('id', 'N/A')}")
                        print(f"     文件名: {file_info.get('filename', 'N/A')}")
                        print(f"     相对路径: {file_info.get('relative_path', 'N/A')}")
                        print(f"     文件大小: {file_info.get('file_size', 0)} 字节")
                        print(f"     扩展名: {file_info.get('extension', 'N/A')}")
                        print(f"     MIME类型: {file_info.get('mime_type', 'N/A')}")
                        
                        # 时间戳信息
                        timestamps = file_info.get('timestamps', {})
                        print(f"     修改时间: {timestamps.get('file_modified', 'N/A')}")
                        print(f"     创建时间: {timestamps.get('created_at', 'N/A')}")
                        
                        # 统计信息
                        statistics = file_info.get('statistics', {})
                        print(f"     查看次数: {statistics.get('view_count', 0)}")
                        print(f"     下载次数: {statistics.get('download_count', 0)}")
                        
                        # 文件类型信息
                        file_type = file_info.get('file_type', {})
                        print(f"     是否图片: {file_type.get('is_image', False)}")
                        print(f"     是否视频: {file_type.get('is_video', False)}")
                        print(f"     是否文档: {file_type.get('is_document', False)}")
                        
                        if len(files) > 5:
                            print(f"\n   ... 还有 {len(files) - 5} 个文件")
                        break
                else:
                    print(f"❌ 获取文件列表失败: {files_result.get('error', '未知错误')}")
        
        print("\n" + "=" * 50)
        print("✅ 文件信息测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_file_scan():
    """测试文件扫描功能"""
    print("\n🔄 测试文件扫描功能")
    print("=" * 50)
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        db_manager.initialize()

        # 初始化文件服务
        file_service = FileService(db_manager)
        
        # 获取第一个共享文件夹
        folders_result = file_service.get_shared_folders()
        if not folders_result.get('success', False):
            print("❌ 无法获取共享文件夹")
            return
        
        folders = folders_result.get('folders', [])
        if not folders:
            print("❌ 没有共享文件夹")
            return
        
        folder = folders[0]
        folder_id = folder.get('id')
        folder_name = folder.get('name', 'Unknown')
        
        print(f"📂 扫描文件夹: {folder_name} (ID: {folder_id})")
        
        # 执行扫描
        scan_result = file_service.scan_shared_folder(folder_id)
        
        if scan_result.get('success', False):
            scanned_count = scan_result.get('scanned_count', 0)
            total_size = scan_result.get('total_size', 0)
            print(f"✅ 扫描完成")
            print(f"   扫描文件数: {scanned_count}")
            print(f"   总大小: {total_size} 字节")
            
            # 重新获取文件列表验证
            files_result = file_service.get_folder_files(folder_id, 1, 100, None)
            if files_result.get('success', False):
                files = files_result.get('files', [])
                print(f"   数据库中文件数: {len(files)}")
                
                # 检查文件信息是否正确
                valid_files = 0
                for file_info in files:
                    if (file_info.get('file_size', 0) > 0 and 
                        file_info.get('extension') and 
                        file_info.get('timestamps', {}).get('file_modified')):
                        valid_files += 1
                
                print(f"   有效文件信息数: {valid_files}")
                print(f"   信息完整率: {valid_files/len(files)*100:.1f}%" if files else "0%")
        else:
            print(f"❌ 扫描失败: {scan_result.get('error', '未知错误')}")
        
    except Exception as e:
        print(f"❌ 扫描测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_file_info()
    test_file_scan()
