# 企业级文件共享系统 - 服务端

## 系统概述

这是一个基于Python开发的企业级文件共享系统服务端，具有以下特性：

### 核心功能
- **权限管理**: 支持内网/外网访问控制，不同用户组权限设置
- **双搜索引擎**: 
  - 文本搜索：类似Everything的快速文件名搜索
  - 图像搜索：基于OpenCV的图像识别搜索
- **缩略图支持**: 支持JPG、PNG、PSD、TIF、AI、EPS等多种格式
- **加密下载**: 下载N次后自动加密，密码保护
- **用户管理**: 完整的用户注册、登录、权限管理系统
- **活动日志**: 详细的用户行为记录和监控
- **实时监控**: 系统性能监控和在线用户管理
- **滚动通知**: 支持截图的通知系统

### 技术特性
- **Windows兼容**: 专为Windows环境设计，无需Docker
- **WinForms界面**: 基于tkinter的桌面管理界面
- **MySQL数据库**: 使用MySQL存储数据
- **RESTful API**: 完整的HTTP API接口
- **WebSocket支持**: 实时通信和通知
- **中文支持**: 完整的中文语言支持

## 系统要求

### 软件要求
- Python 3.7+
- MySQL 5.7+ 或 MySQL 8.0+
- Windows 7/10/11

### 硬件要求
- CPU: 双核2GHz以上
- 内存: 4GB以上
- 硬盘: 10GB以上可用空间
- 网络: 100Mbps以上

## 安装和配置

### 1. 环境准备

#### 安装Python
1. 下载Python 3.7+: https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"
3. 验证安装: `python --version`

#### 安装MySQL
1. 下载MySQL: https://dev.mysql.com/downloads/mysql/
2. 安装并设置root密码为: `123456`
3. 启动MySQL服务

### 2. 项目部署

#### 下载项目
```bash
# 解压项目文件到目标目录
cd C:\Users\<USER>\Desktop\Net\backend
```

#### 安装依赖
```bash
# 方法1: 使用启动脚本自动安装
python start_server.py

# 方法2: 手动安装
pip install -r requirements.txt
```

#### 配置数据库
系统会自动创建数据库和表结构，默认配置：
- 主机: localhost
- 端口: 3306
- 用户名: root
- 密码: 123456
- 数据库: file_share_system

如需修改，请编辑 `config/settings.py` 文件。

### 3. 启动系统

#### 快速启动
```bash
python start_server.py
```

#### 手动启动
```bash
python main.py
```

## 使用说明

### 服务端管理界面

启动后会显示主管理窗口，包含以下功能：

#### 1. 系统概览
- 显示系统统计信息
- 在线用户数量
- 文件和下载统计
- 快速操作按钮

#### 2. 实时监控
- 在线用户列表
- 用户活动监控
- 系统性能指标

#### 3. 系统日志
- 实时日志显示
- 错误和警告信息
- 操作记录

#### 4. 系统通知
- 发送系统通知
- 通知历史记录

### 主要管理功能

#### 用户管理
- 创建/删除用户
- 设置用户权限
- 用户组管理
- 登录限制设置

#### 文件管理
- 添加共享文件夹
- 设置文件夹权限
- 文件扫描和索引
- 缩略图生成

#### 权限控制
- 内网/外网访问控制
- 文件操作权限（读/写/删除）
- 下载权限管理
- 敏感文件保护

#### 监控和日志
- 用户活动监控
- 下载行为记录
- 搜索统计
- 系统性能监控

## API接口

系统提供完整的RESTful API接口：

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 文件接口
- `GET /api/files/folders` - 获取共享文件夹
- `GET /api/files/folders/{id}/files` - 获取文件列表
- `GET /api/files/{id}/download` - 下载文件

### 搜索接口
- `POST /api/search` - 文件搜索

### 管理接口
- `GET /api/admin/users` - 用户管理
- `GET /api/admin/stats` - 系统统计

## 配置说明

### 主配置文件: config/settings.py

```python
# 服务器设置
server:
  host: "0.0.0.0"
  port: 8080

# 数据库设置  
database:
  host: "localhost"
  username: "root"
  password: "123456"
  database: "file_share_system"

# 文件共享设置
file_share:
  max_file_size: **********  # 1GB
  allowed_extensions: [".jpg", ".png", ".pdf", ...]

# 安全设置
security:
  enable_registration: false
  require_license_key: true
  max_login_attempts: 5
```

## 故障排除

### 常见问题

#### 1. MySQL连接失败
- 检查MySQL服务是否启动
- 验证用户名密码是否正确
- 确认端口3306未被占用

#### 2. 依赖包安装失败
- 检查网络连接
- 使用国内镜像源: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

#### 3. 权限错误
- 确保Python有足够的文件系统权限
- 以管理员身份运行

#### 4. 端口占用
- 修改配置文件中的端口号
- 或关闭占用端口的其他程序

### 日志文件位置
- 系统日志: `logs/FileShareSystem.log`
- 错误日志: `logs/FileShareSystem_error.log`
- 数据库日志: `logs/DatabaseLogger.log`

## 开发说明

### 项目结构
```
backend/
├── main.py                 # 主程序入口
├── start_server.py         # 启动脚本
├── requirements.txt        # 依赖包列表
├── config/                 # 配置模块
├── models/                 # 数据模型
├── services/               # 业务服务
├── api/                    # API接口
├── gui/                    # GUI界面
├── utils/                  # 工具模块
├── data/                   # 数据目录
└── logs/                   # 日志目录
```

### 扩展开发
- 添加新的API接口: 在 `api/routes/` 目录下创建新文件
- 添加新的服务: 在 `services/` 目录下创建新服务类
- 修改数据模型: 在 `models/` 目录下修改或添加模型

## 许可证

本软件为企业级商业软件，请联系开发团队获取许可证。

## 技术支持

如有问题请联系技术支持团队。
