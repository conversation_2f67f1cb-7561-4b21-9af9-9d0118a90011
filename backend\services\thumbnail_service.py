#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缩略图生成服务
"""

import os
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import threading
import queue
import time

from utils.logger import setup_logger

# 可选导入图像处理库
try:
    from PIL import Image, ImageOps
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

try:
    import cv2
    import numpy as np
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

class ThumbnailService:
    """缩略图生成服务"""
    
    def __init__(self, thumbnail_dir: str = "./data/thumbnails"):
        self.thumbnail_dir = Path(thumbnail_dir)
        self.thumbnail_dir.mkdir(parents=True, exist_ok=True)
        self.logger = setup_logger("ThumbnailService")
        
        # 支持的图像格式
        self.supported_formats = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'
        }
        
        # 缩略图尺寸配置
        self.thumbnail_sizes = {
            'small': (150, 150),
            'medium': (300, 300),
            'large': (600, 600),
            'xlarge': (1200, 1200)
        }
        
        # 任务队列
        self.task_queue = queue.Queue()
        self.worker_threads = []
        self.running = False
        
        # 启动工作线程
        self.start_workers()
    
    def start_workers(self, num_workers: int = 2):
        """启动工作线程"""
        self.running = True
        
        for i in range(num_workers):
            worker = threading.Thread(target=self._worker_loop, daemon=True)
            worker.start()
            self.worker_threads.append(worker)
        
        self.logger.info(f"缩略图服务启动，工作线程数: {num_workers}")
    
    def stop_workers(self):
        """停止工作线程"""
        self.running = False
        
        # 添加停止信号到队列
        for _ in self.worker_threads:
            self.task_queue.put(None)
        
        # 等待线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.logger.info("缩略图服务已停止")
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.running:
            try:
                task = self.task_queue.get(timeout=1)
                if task is None:  # 停止信号
                    break
                
                self._process_task(task)
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理缩略图任务失败: {e}")
    
    def _process_task(self, task: Dict[str, Any]):
        """处理缩略图生成任务"""
        try:
            file_path = task['file_path']
            sizes = task.get('sizes', ['medium'])
            callback = task.get('callback')
            
            results = {}
            
            for size in sizes:
                thumbnail_path = self.generate_thumbnail(file_path, size)
                if thumbnail_path:
                    results[size] = thumbnail_path
            
            if callback:
                callback(file_path, results)
                
        except Exception as e:
            self.logger.error(f"处理缩略图任务失败: {e}")
    
    def generate_thumbnail_async(self, file_path: str, sizes: list = None, 
                                callback=None) -> bool:
        """异步生成缩略图"""
        try:
            if not self.is_supported_format(file_path):
                return False
            
            task = {
                'file_path': file_path,
                'sizes': sizes or ['medium'],
                'callback': callback
            }
            
            self.task_queue.put(task)
            return True
            
        except Exception as e:
            self.logger.error(f"添加缩略图任务失败: {e}")
            return False
    
    def generate_thumbnail(self, file_path: str, size: str = 'medium') -> Optional[str]:
        """生成缩略图"""
        try:
            if not os.path.exists(file_path):
                return None
            
            if not self.is_supported_format(file_path):
                return None
            
            # 生成缩略图文件名
            thumbnail_path = self._get_thumbnail_path(file_path, size)
            
            # 如果缩略图已存在且较新，直接返回
            if os.path.exists(thumbnail_path):
                if os.path.getmtime(thumbnail_path) >= os.path.getmtime(file_path):
                    return thumbnail_path
            
            # 获取目标尺寸
            target_size = self.thumbnail_sizes.get(size, (300, 300))
            
            # 生成缩略图
            if HAS_PIL:
                success = self._generate_with_pil(file_path, thumbnail_path, target_size)
            elif HAS_OPENCV:
                success = self._generate_with_opencv(file_path, thumbnail_path, target_size)
            else:
                self.logger.warning("没有可用的图像处理库")
                return None
            
            if success:
                self.logger.debug(f"缩略图生成成功: {thumbnail_path}")
                return thumbnail_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"生成缩略图失败 {file_path}: {e}")
            return None
    
    def _generate_with_pil(self, file_path: str, thumbnail_path: str, 
                          target_size: Tuple[int, int]) -> bool:
        """使用PIL生成缩略图"""
        try:
            with Image.open(file_path) as image:
                # 转换为RGB模式（处理RGBA等格式）
                if image.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                elif image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # 生成缩略图（保持宽高比）
                image.thumbnail(target_size, Image.Resampling.LANCZOS)
                
                # 创建目标目录
                os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
                
                # 保存缩略图
                image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                
                return True
                
        except Exception as e:
            self.logger.error(f"PIL生成缩略图失败: {e}")
            return False
    
    def _generate_with_opencv(self, file_path: str, thumbnail_path: str, 
                             target_size: Tuple[int, int]) -> bool:
        """使用OpenCV生成缩略图"""
        try:
            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return False
            
            # 获取原始尺寸
            height, width = image.shape[:2]
            
            # 计算缩放比例（保持宽高比）
            scale = min(target_size[0] / width, target_size[1] / height)
            
            # 计算新尺寸
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # 缩放图像
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 创建目标目录
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, resized, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            return True
            
        except Exception as e:
            self.logger.error(f"OpenCV生成缩略图失败: {e}")
            return False
    
    def _get_thumbnail_path(self, file_path: str, size: str) -> str:
        """获取缩略图路径"""
        # 生成文件哈希作为文件名
        file_hash = self._get_file_hash(file_path)
        
        # 构建缩略图路径
        thumbnail_filename = f"{file_hash}_{size}.jpg"
        return str(self.thumbnail_dir / size / thumbnail_filename)
    
    def _get_file_hash(self, file_path: str) -> str:
        """获取文件哈希"""
        try:
            # 使用文件路径和修改时间生成哈希
            stat = os.stat(file_path)
            hash_input = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except:
            # 如果获取文件信息失败，使用文件路径
            return hashlib.md5(file_path.encode()).hexdigest()
    
    def is_supported_format(self, file_path: str) -> bool:
        """检查是否支持的格式"""
        _, ext = os.path.splitext(file_path)
        return ext.lower() in self.supported_formats
    
    def get_thumbnail_path(self, file_path: str, size: str = 'medium') -> Optional[str]:
        """获取缩略图路径（如果存在）"""
        thumbnail_path = self._get_thumbnail_path(file_path, size)
        return thumbnail_path if os.path.exists(thumbnail_path) else None
    
    def delete_thumbnails(self, file_path: str) -> bool:
        """删除文件的所有缩略图"""
        try:
            deleted_count = 0
            
            for size in self.thumbnail_sizes.keys():
                thumbnail_path = self._get_thumbnail_path(file_path, size)
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
                    deleted_count += 1
            
            if deleted_count > 0:
                self.logger.debug(f"删除缩略图: {file_path}, 数量: {deleted_count}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除缩略图失败: {e}")
            return False
    
    def cleanup_orphaned_thumbnails(self) -> int:
        """清理孤立的缩略图"""
        try:
            cleaned_count = 0
            
            for size_dir in self.thumbnail_dir.iterdir():
                if size_dir.is_dir() and size_dir.name in self.thumbnail_sizes:
                    for thumbnail_file in size_dir.glob("*.jpg"):
                        # 这里可以添加逻辑检查原文件是否还存在
                        # 由于没有反向映射，暂时跳过
                        pass
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理孤立缩略图失败: {e}")
            return 0
    
    def get_thumbnail_statistics(self) -> Dict[str, Any]:
        """获取缩略图统计信息"""
        try:
            stats = {
                'total_thumbnails': 0,
                'total_size': 0,
                'sizes': {}
            }
            
            for size in self.thumbnail_sizes.keys():
                size_dir = self.thumbnail_dir / size
                if size_dir.exists():
                    size_count = 0
                    size_total = 0
                    
                    for thumbnail_file in size_dir.glob("*.jpg"):
                        size_count += 1
                        size_total += thumbnail_file.stat().st_size
                    
                    stats['sizes'][size] = {
                        'count': size_count,
                        'total_size': size_total
                    }
                    
                    stats['total_thumbnails'] += size_count
                    stats['total_size'] += size_total
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取缩略图统计失败: {e}")
            return {}
    
    def batch_generate_thumbnails(self, file_paths: list, sizes: list = None) -> Dict[str, Any]:
        """批量生成缩略图"""
        try:
            results = {
                'success': 0,
                'failed': 0,
                'skipped': 0,
                'details': {}
            }
            
            sizes = sizes or ['medium']
            
            for file_path in file_paths:
                if not self.is_supported_format(file_path):
                    results['skipped'] += 1
                    continue
                
                file_results = {}
                success = True
                
                for size in sizes:
                    thumbnail_path = self.generate_thumbnail(file_path, size)
                    if thumbnail_path:
                        file_results[size] = thumbnail_path
                    else:
                        success = False
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                
                results['details'][file_path] = file_results
            
            self.logger.info(f"批量生成缩略图完成: 成功 {results['success']}, 失败 {results['failed']}, 跳过 {results['skipped']}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量生成缩略图失败: {e}")
            return {'success': 0, 'failed': len(file_paths), 'skipped': 0, 'details': {}}
