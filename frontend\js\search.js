/**
 * 搜索功能模块
 * 处理文件搜索、搜索建议、搜索历史等功能
 */

class SearchManager {
    constructor() {
        this.searchInput = null;
        this.searchFilters = [];
        this.currentQuery = '';
        this.currentType = 'all';
        this.searchResults = [];
        this.searchHistory = Utils.storage.get('search_history', []);
        this.isSearching = false;
        
        this.init();
    }
    
    /**
     * 初始化搜索管理器
     */
    init() {
        this.bindEvents();
        this.setupSearchFilters();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        this.searchInput = Utils.dom.$('#search-input');
        if (!this.searchInput) return;
        
        // 搜索输入防抖
        const debouncedSearch = Utils.debounce(
            (query) => this.performSearch(query),
            CONFIG.UI.SEARCH.DEBOUNCE_DELAY
        );
        
        Utils.event.on(this.searchInput, 'input', (e) => {
            const query = e.target.value.trim();
            this.currentQuery = query;
            
            if (query.length >= CONFIG.UI.SEARCH.MIN_QUERY_LENGTH) {
                debouncedSearch(query);
            } else {
                this.clearSearchResults();
            }
        });
        
        // 回车搜索
        Utils.event.on(this.searchInput, 'keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(this.currentQuery);
            }
            
            // ESC 清除搜索
            if (e.key === 'Escape') {
                this.clearSearch();
            }
        });
        
        // 搜索焦点
        Utils.event.on(this.searchInput, 'focus', () => {
            this.showSearchSuggestions();
        });
        
        Utils.event.on(this.searchInput, 'blur', () => {
            // 延迟隐藏，允许点击建议
            setTimeout(() => this.hideSearchSuggestions(), 200);
        });
    }
    
    /**
     * 设置搜索过滤器
     */
    setupSearchFilters() {
        this.searchFilters = Utils.dom.$$('.filter-btn');
        
        this.searchFilters.forEach(filter => {
            Utils.event.on(filter, 'click', (e) => {
                const type = e.target.dataset.type;
                this.setSearchType(type);
                
                // 如果有搜索内容，重新搜索
                if (this.currentQuery) {
                    this.performSearch(this.currentQuery);
                }
            });
        });
    }
    
    /**
     * 设置搜索类型
     */
    setSearchType(type) {
        this.currentType = type;
        
        // 更新过滤器按钮状态
        this.searchFilters.forEach(filter => {
            Utils.dom.removeClass(filter, 'active');
            if (filter.dataset.type === type) {
                Utils.dom.addClass(filter, 'active');
            }
        });
    }
    
    /**
     * 执行搜索
     */
    async performSearch(query) {
        if (!query || query.length < CONFIG.UI.SEARCH.MIN_QUERY_LENGTH) {
            return;
        }
        
        try {
            this.isSearching = true;
            this.showSearchLoading();
            
            const results = await SearchAPI.search(query, this.currentType, {
                limit: CONFIG.UI.SEARCH.MAX_RESULTS
            });
            
            this.searchResults = results.files || [];
            this.renderSearchResults();
            
            // 添加到搜索历史
            this.addToHistory(query);
            
        } catch (error) {
            CONFIG.log('error', 'Search failed:', error);
            Components.Toast.error('搜索失败，请重试');
            this.clearSearchResults();
        } finally {
            this.isSearching = false;
            this.hideSearchLoading();
        }
    }
    
    /**
     * 渲染搜索结果
     */
    renderSearchResults() {
        if (!fileManager) return;
        
        // 更新文件管理器显示搜索结果
        fileManager.files = this.searchResults;
        fileManager.renderFiles();
        
        // 更新面包屑显示搜索状态
        this.updateSearchBreadcrumb();
        
        // 显示搜索结果统计
        this.showSearchStats();
    }
    
    /**
     * 更新搜索面包屑
     */
    updateSearchBreadcrumb() {
        const breadcrumbNav = Utils.dom.$('.breadcrumb-nav');
        if (!breadcrumbNav) return;
        
        breadcrumbNav.innerHTML = `
            <a href="#" class="breadcrumb-item" onclick="searchManager.clearSearch()">
                <i class="fas fa-home"></i>
                首页
            </a>
            <span class="breadcrumb-item">
                <i class="fas fa-search"></i>
                搜索: "${this.currentQuery}"
            </span>
        `;
    }
    
    /**
     * 显示搜索统计
     */
    showSearchStats() {
        const resultCount = this.searchResults.length;
        const typeText = this.getTypeText(this.currentType);
        
        Components.Toast.info(
            `找到 ${resultCount} 个${typeText}文件`,
            2000
        );
    }
    
    /**
     * 获取类型文本
     */
    getTypeText(type) {
        const typeMap = {
            all: '',
            image: '图片',
            document: '文档',
            video: '视频',
            audio: '音频'
        };
        return typeMap[type] || '';
    }
    
    /**
     * 显示搜索加载状态
     */
    showSearchLoading() {
        const searchContainer = this.searchInput.parentElement;
        if (searchContainer) {
            Utils.dom.addClass(searchContainer, 'searching');
        }
    }
    
    /**
     * 隐藏搜索加载状态
     */
    hideSearchLoading() {
        const searchContainer = this.searchInput.parentElement;
        if (searchContainer) {
            Utils.dom.removeClass(searchContainer, 'searching');
        }
    }
    
    /**
     * 清除搜索结果
     */
    clearSearchResults() {
        this.searchResults = [];
        
        if (fileManager) {
            // 恢复原始文件列表
            fileManager.loadFiles(fileManager.currentFolder?.id);
        }
    }
    
    /**
     * 清除搜索
     */
    clearSearch() {
        this.currentQuery = '';
        this.searchInput.value = '';
        this.clearSearchResults();
        this.hideSearchSuggestions();
    }
    
    /**
     * 添加到搜索历史
     */
    addToHistory(query) {
        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        
        // 添加到开头
        this.searchHistory.unshift(query);
        
        // 限制历史记录数量
        if (this.searchHistory.length > 10) {
            this.searchHistory = this.searchHistory.slice(0, 10);
        }
        
        // 保存到本地存储
        Utils.storage.set('search_history', this.searchHistory);
    }
    
    /**
     * 显示搜索建议
     */
    showSearchSuggestions() {
        if (this.searchHistory.length === 0) return;
        
        const searchContainer = this.searchInput.parentElement;
        let suggestionsContainer = searchContainer.querySelector('.search-suggestions');
        
        if (!suggestionsContainer) {
            suggestionsContainer = Utils.dom.create('div', {
                className: 'search-suggestions'
            });
            searchContainer.appendChild(suggestionsContainer);
        }
        
        suggestionsContainer.innerHTML = `
            <div class="suggestions-header">搜索历史</div>
            ${this.searchHistory.map(query => `
                <div class="suggestion-item" data-query="${query}">
                    <i class="fas fa-history"></i>
                    <span>${query}</span>
                    <button class="remove-suggestion" data-query="${query}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('')}
            <div class="suggestions-footer">
                <button class="clear-history-btn">清除历史</button>
            </div>
        `;
        
        // 绑定建议点击事件
        Utils.event.on(suggestionsContainer, 'click', (e) => {
            const suggestionItem = e.target.closest('.suggestion-item');
            const removeBtn = e.target.closest('.remove-suggestion');
            const clearBtn = e.target.closest('.clear-history-btn');
            
            if (removeBtn) {
                e.stopPropagation();
                const query = removeBtn.dataset.query;
                this.removeFromHistory(query);
                this.showSearchSuggestions();
            } else if (clearBtn) {
                this.clearHistory();
                this.hideSearchSuggestions();
            } else if (suggestionItem) {
                const query = suggestionItem.dataset.query;
                this.searchInput.value = query;
                this.currentQuery = query;
                this.performSearch(query);
                this.hideSearchSuggestions();
            }
        });
        
        Utils.dom.addClass(suggestionsContainer, 'show');
    }
    
    /**
     * 隐藏搜索建议
     */
    hideSearchSuggestions() {
        const searchContainer = this.searchInput.parentElement;
        const suggestionsContainer = searchContainer.querySelector('.search-suggestions');
        
        if (suggestionsContainer) {
            Utils.dom.removeClass(suggestionsContainer, 'show');
        }
    }
    
    /**
     * 从历史中移除
     */
    removeFromHistory(query) {
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        Utils.storage.set('search_history', this.searchHistory);
    }
    
    /**
     * 清除搜索历史
     */
    clearHistory() {
        this.searchHistory = [];
        Utils.storage.set('search_history', this.searchHistory);
    }
    
    /**
     * 高级搜索
     */
    showAdvancedSearch() {
        // TODO: 实现高级搜索界面
        Components.Toast.info('高级搜索功能开发中...');
    }
    
    /**
     * 搜索建议
     */
    async getSearchSuggestions(query) {
        try {
            // TODO: 实现搜索建议API
            return [];
        } catch (error) {
            CONFIG.log('error', 'Failed to get search suggestions:', error);
            return [];
        }
    }
    
    /**
     * 保存搜索
     */
    saveSearch(name, query, type) {
        const savedSearches = Utils.storage.get('saved_searches', []);
        
        const searchItem = {
            id: Utils.generateId('search'),
            name,
            query,
            type,
            createdAt: new Date().toISOString()
        };
        
        savedSearches.push(searchItem);
        Utils.storage.set('saved_searches', savedSearches);
        
        Components.Toast.success('搜索已保存');
    }
    
    /**
     * 获取保存的搜索
     */
    getSavedSearches() {
        return Utils.storage.get('saved_searches', []);
    }
    
    /**
     * 删除保存的搜索
     */
    deleteSavedSearch(searchId) {
        const savedSearches = this.getSavedSearches();
        const filteredSearches = savedSearches.filter(search => search.id !== searchId);
        Utils.storage.set('saved_searches', filteredSearches);
        
        Components.Toast.success('搜索已删除');
    }
    
    /**
     * 执行保存的搜索
     */
    executeSavedSearch(searchId) {
        const savedSearches = this.getSavedSearches();
        const search = savedSearches.find(s => s.id === searchId);
        
        if (search) {
            this.searchInput.value = search.query;
            this.currentQuery = search.query;
            this.setSearchType(search.type);
            this.performSearch(search.query);
        }
    }
}

// 创建全局搜索管理器实例
let searchManager;

document.addEventListener('DOMContentLoaded', () => {
    searchManager = new SearchManager();
});

// 全局可用
window.SearchManager = SearchManager;
window.searchManager = null;
